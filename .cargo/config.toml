# Cross-compilation configuration

# Windows GNU target - static linking to avoid VCRUNTIME140.dll dependency
[target.x86_64-pc-windows-gnu]
rustflags = [
    "-C", "target-feature=+crt-static"
]

# ARM64 Linux musl - static linking for better compatibility
[target.aarch64-unknown-linux-musl]
linker = "aarch64-linux-gnu-gcc"
rustflags = [
    "-C", "target-feature=+crt-static"
]

# Set environment variables for cross-compilation
[env]
# x86_64 musl target - use musl-compatible toolchain
CC_x86_64_unknown_linux_musl = "x86_64-linux-musl-gcc"
CXX_x86_64_unknown_linux_musl = "x86_64-linux-musl-g++"
AR_x86_64_unknown_linux_musl = "x86_64-linux-musl-ar"

# ARM64 musl target
CC_aarch64_unknown_linux_musl = "aarch64-linux-gnu-gcc"
CXX_aarch64_unknown_linux_musl = "aarch64-linux-gnu-g++"
AR_aarch64_unknown_linux_musl = "aarch64-linux-gnu-ar"

# x86_64 Linux musl - ensure static linking
[target.x86_64-unknown-linux-musl]
rustflags = [
    "-C", "target-feature=+crt-static"
]
