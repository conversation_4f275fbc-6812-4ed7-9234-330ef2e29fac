# Cross-compilation configuration

# Windows GNU target - static linking to avoid VCRUNTIME140.dll and libstdc++ dependencies
[target.x86_64-pc-windows-gnu]
rustflags = [
    "-C", "target-feature=+crt-static",
    "-C", "link-arg=-static-libgcc",
    "-C", "link-arg=-static-libstdc++"
]





# x86_64 Linux musl - ensure static linking
[target.x86_64-unknown-linux-musl]
rustflags = [
    "-C", "target-feature=+crt-static"
]
