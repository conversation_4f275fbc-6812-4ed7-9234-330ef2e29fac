# Cross-compilation configuration

# Windows GNU target - static linking to avoid VCRUNTIME140.dll dependency
[target.x86_64-pc-windows-gnu]
rustflags = [
    "-C", "target-feature=+crt-static"
]

# ARM64 Linux musl - static linking for better compatibility
[target.aarch64-unknown-linux-musl]
linker = "aarch64-linux-gnu-gcc"
rustflags = [
    "-C", "target-feature=+crt-static"
]

# x86_64 Linux musl - ensure static linking
[target.x86_64-unknown-linux-musl]
rustflags = [
    "-C", "target-feature=+crt-static"
]
