name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release (e.g., v1.0.0)'
        required: true
        type: string
      create_release:
        description: 'Create a GitHub release'
        required: false
        default: true
        type: boolean

env:
  CARGO_TERM_COLOR: always
  TAG_NAME: ${{ github.event.inputs.tag_name || github.ref_name }}

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
      - name: Run tests
        run: cargo test --all-features

  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy
      - uses: Swatinem/rust-cache@v2
      - name: Run clippy
        run: cargo clippy --all-targets --all-features -- -D warnings

  fmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt
      - name: Check formatting
        run: cargo fmt --all -- --check

  publish:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    needs: [test, clippy, fmt]
    if: ${{ github.event_name == 'push' && startsWith(github.ref, 'refs/tags/') }}
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
      - name: Publish to crates.io
        run: cargo publish --token ${{ secrets.CRATES_IO_TOKEN }}

  build:
    name: Build Binaries
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            use_cross: true

          - os: macos-latest
            target: x86_64-apple-darwin
            use_cross: false
          - os: macos-latest
            target: aarch64-apple-darwin
            use_cross: false
          - os: windows-latest
            target: x86_64-pc-windows-gnu
            use_cross: false
            setup_target: true
    runs-on: ${{ matrix.os }}
    needs: [test, clippy, fmt]
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      - uses: Swatinem/rust-cache@v2

      # Install cross-compilation tools
      - name: Install cross
        if: matrix.use_cross
        run: cargo install cross --version 0.2.5

      # Setup Windows GNU toolchain with static libraries
      - name: Setup Windows GNU toolchain
        if: matrix.target == 'x86_64-pc-windows-gnu'
        run: |
          # Install MSYS2 which includes MinGW-w64 with static libraries
          choco install msys2 -y

          # Update MSYS2 and install the full MinGW-w64 toolchain
          C:\msys64\usr\bin\bash.exe -lc "pacman -Syu --noconfirm"
          C:\msys64\usr\bin\bash.exe -lc "pacman -S --noconfirm mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake"

          # Add MinGW-w64 to PATH
          echo "C:\msys64\mingw64\bin" >> $GITHUB_PATH

          # Add Rust target
          rustup target add x86_64-pc-windows-gnu
        shell: pwsh





      # Build binaries
      - name: Build binaries
        run: |
          if [ "${{ matrix.use_cross }}" = "true" ]; then
            cross build --release --target ${{ matrix.target }} --bin colonyd
            cross build --release --target ${{ matrix.target }} --bin colony
          else
            cargo build --release --target ${{ matrix.target }} --bin colonyd
            cargo build --release --target ${{ matrix.target }} --bin colony
          fi
        shell: bash
        env:
          # Set RUSTFLAGS for static linking on musl and Windows GNU targets
          RUSTFLAGS: ${{ matrix.target == 'x86_64-unknown-linux-musl' && '-C target-feature=+crt-static' || matrix.target == 'x86_64-pc-windows-gnu' && '-C target-feature=+crt-static -C link-arg=-static-libgcc -C link-arg=-static-libstdc++' || '' }}
          # Additional environment variables
          CARGO_BUILD_TARGET_DIR: ${{ github.workspace }}/target

      # Upload artifacts
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: binaries-${{ matrix.target }}
          path: |
            target/${{ matrix.target }}/release/colonyd*
            target/${{ matrix.target }}/release/colony*

  artifacts-summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [build]
    if: ${{ github.event.inputs.create_release == 'false' }}
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: List built artifacts
        run: |
          echo "## Built Artifacts" >> $GITHUB_STEP_SUMMARY
          echo "The following binaries were successfully built:" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          find artifacts -type f -name "colony*" | while read file; do
            size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "unknown")
            echo "- \`$(basename "$file")\` (${size} bytes)" >> $GITHUB_STEP_SUMMARY
          done

  release:
    name: Create Release and Upload Binaries
    runs-on: ubuntu-latest
    needs: [build]
    if: ${{ github.event.inputs.create_release != 'false' }}
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4

      # Download all artifacts
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      # Prepare release assets
      - name: Prepare release assets
        run: |
          mkdir -p release-assets

          # Linux x86_64
          cp artifacts/binaries-x86_64-unknown-linux-musl/colonyd release-assets/colonyd-x86_64-unknown-linux-musl
          cp artifacts/binaries-x86_64-unknown-linux-musl/colony release-assets/colony-x86_64-unknown-linux-musl



          # macOS x86_64 (Intel)
          cp artifacts/binaries-x86_64-apple-darwin/colonyd release-assets/colonyd-x86_64-apple-darwin
          cp artifacts/binaries-x86_64-apple-darwin/colony release-assets/colony-x86_64-apple-darwin

          # macOS ARM64 (Apple Silicon)
          cp artifacts/binaries-aarch64-apple-darwin/colonyd release-assets/colonyd-aarch64-apple-darwin
          cp artifacts/binaries-aarch64-apple-darwin/colony release-assets/colony-aarch64-apple-darwin

          # Windows x86_64
          cp artifacts/binaries-x86_64-pc-windows-gnu/colonyd.exe release-assets/colonyd-x86_64-pc-windows-gnu.exe
          cp artifacts/binaries-x86_64-pc-windows-gnu/colony.exe release-assets/colony-x86_64-pc-windows-gnu.exe

      # Create release and upload assets
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ env.TAG_NAME }}
          name: Release ${{ env.TAG_NAME }}
          files: release-assets/*
          generate_release_notes: true
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

