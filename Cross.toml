[build.env]
passthrough = [
    "CARGO_HOME",
    "CARGO_TARGET_DIR",
]

[target.x86_64-pc-windows-gnu]
image = "ghcr.io/cross-rs/x86_64-pc-windows-gnu:0.2.5"
[target.x86_64-pc-windows-gnu.env]
passthrough = [
    "RUSTFLAGS"
]

[target.x86_64-unknown-linux-musl]
image = "ghcr.io/cross-rs/x86_64-unknown-linux-musl:0.2.5"

[target.aarch64-unknown-linux-musl]
# Use messense's image which is known to work better with ARM64 musl
image = "messense/rust-musl-cross:aarch64-musl"
[target.aarch64-unknown-linux-musl.env]
passthrough = [
    "RUSTFLAGS",
    "CARGO_BUILD_TARGET_DIR",
    "CARGO_NET_GIT_FETCH_WITH_CLI"
]

[target.x86_64-apple-darwin]
image = "ghcr.io/cross-rs/x86_64-apple-darwin:0.2.5"
